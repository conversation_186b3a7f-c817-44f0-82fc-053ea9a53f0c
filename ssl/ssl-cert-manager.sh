#!/bin/bash

# SSL证书管理脚本
# 用于申请和管理多个域名的SSL证书
# 作者: SSL证书自动化管理
# 使用方法: ./ssl-cert-manager.sh [域名]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
EMAIL="${SSL_EMAIL:-<EMAIL>}"
CERT_DIR="${SSL_CERT_DIR:-/root/certs}"
NGINX_SERVICE="${NGINX_SERVICE:-/etc/init.d/nginx}"
LOG_FILE="${SSL_LOG_FILE:-/tmp/ssl-cert-manager.log}"
NGINX_CONF_DIR="${NGINX_CONF_DIR:-/etc/nginx/sites-available}"
NGINX_ENABLED_DIR="${NGINX_ENABLED_DIR:-/etc/nginx/sites-enabled}"

# Cloudflare API配置
CF_Token="${CF_Token:-}"
CF_Account_ID="${CF_Account_ID:-}"
CF_Zone_ID="${CF_Zone_ID:-}"

# 启用详细日志
VERBOSE="${SSL_VERBOSE:-1}"

# 检测nginx服务管理方式
detect_nginx_service() {
    log_debug "开始检测nginx服务管理方式"

    # 首先检查systemctl是否可用
    if command -v systemctl >/dev/null 2>&1; then
        log_debug "检测到systemctl命令"
        # 使用timeout避免卡住，检查nginx服务是否存在
        if timeout 5 systemctl list-units --type=service | grep -q "nginx.service" 2>/dev/null; then
            log_debug "检测到nginx systemd服务"
            NGINX_SERVICE="systemctl"
            NGINX_CMD_STOP="systemctl stop nginx"
            NGINX_CMD_RESTART="systemctl restart nginx"
            log_debug "使用systemctl管理nginx"
            return 0
        fi
    fi

    # 检查传统的init.d脚本
    if [ -f "/etc/init.d/nginx" ]; then
        log_debug "检测到nginx init.d脚本"
        NGINX_SERVICE="/etc/init.d/nginx"
        NGINX_CMD_STOP="/etc/init.d/nginx stop"
        NGINX_CMD_RESTART="/etc/init.d/nginx restart"
        log_debug "使用init.d管理nginx"
        return 0
    fi

    # 检查其他可能的nginx路径
    for nginx_path in "/usr/sbin/nginx" "/usr/local/nginx/sbin/nginx" "/opt/nginx/sbin/nginx"; do
        if [ -f "$nginx_path" ]; then
            log_debug "检测到nginx二进制文件: $nginx_path"
            NGINX_SERVICE="manual"
            NGINX_CMD_STOP="pkill nginx || true"
            NGINX_CMD_RESTART="pkill nginx || true; sleep 2; $nginx_path"
            log_debug "使用手动方式管理nginx"
            return 0
        fi
    done

    log_error "无法找到nginx服务管理方式"
    log_info "请确保nginx已正确安装"
    exit 1
}

# 日志函数
log_info() {
    local msg="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${BLUE}[INFO]${NC} $msg"
    [ "$VERBOSE" = "1" ] && echo "[$timestamp] [INFO] $msg" >> "$LOG_FILE"
}

log_success() {
    local msg="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${GREEN}[SUCCESS]${NC} $msg"
    [ "$VERBOSE" = "1" ] && echo "[$timestamp] [SUCCESS] $msg" >> "$LOG_FILE"
}

log_warning() {
    local msg="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${YELLOW}[WARNING]${NC} $msg"
    [ "$VERBOSE" = "1" ] && echo "[$timestamp] [WARNING] $msg" >> "$LOG_FILE"
}

log_error() {
    local msg="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${RED}[ERROR]${NC} $msg"
    [ "$VERBOSE" = "1" ] && echo "[$timestamp] [ERROR] $msg" >> "$LOG_FILE"
}

log_debug() {
    local msg="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    [ "$VERBOSE" = "1" ] && {
        echo -e "${NC}[DEBUG] $msg${NC}"
        echo "[$timestamp] [DEBUG] $msg" >> "$LOG_FILE"
    }
}

# 检查系统依赖
check_dependencies() {
    log_debug "开始检查系统依赖"
    local missing_deps=()

    for cmd in curl openssl; do
        log_debug "检查命令: $cmd"
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_deps+=("$cmd")
            log_debug "缺少命令: $cmd"
        else
            log_debug "命令 $cmd 可用"
        fi
    done

    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少必需的依赖: ${missing_deps[*]}"
        log_info "请安装缺少的依赖后重试"
        exit 1
    fi
    log_debug "系统依赖检查完成"
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    if ! curl -s --connect-timeout 10 https://get.acme.sh/ >/dev/null; then
        log_error "无法连接到acme.sh服务器，请检查网络连接"
        exit 1
    fi
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
}

# 检查域名格式（用于交互式输入）
validate_domain_format() {
    local domain=$1
    if [[ $domain =~ ^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$ ]]; then
        return 0
    else
        return 1
    fi
}

# 检查域名格式（用于命令行参数）
validate_domain() {
    local domain=$1
    if [[ ! $domain =~ ^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$ ]]; then
        log_error "域名格式不正确: $domain"
        exit 1
    fi
}

# 初始化acme.sh
init_acme() {
    log_info "检查acme.sh安装状态..."

    if [ ! -d ~/.acme.sh ]; then
        log_info "安装acme.sh..."
        if ! curl -s https://get.acme.sh/ | sh; then
            log_error "acme.sh安装失败"
            exit 1
        fi

        log_info "注册acme.sh账户..."
        if ! ~/.acme.sh/acme.sh --register-account -m "$EMAIL"; then
            log_error "acme.sh账户注册失败"
            exit 1
        fi

        log_success "acme.sh安装完成"
    else
        log_info "acme.sh已安装"
    fi
}

# 停止nginx服务
stop_nginx() {
    log_info "停止nginx服务..."
    if eval "$NGINX_CMD_STOP"; then
        log_success "nginx服务已停止"
    else
        log_warning "nginx服务停止失败或已经停止"
    fi
}

# 重启nginx服务
restart_nginx() {
    log_info "重启nginx服务..."
    if eval "$NGINX_CMD_RESTART"; then
        log_success "nginx服务已重启"
    else
        log_error "nginx服务重启失败"
        exit 1
    fi
}

# 创建证书目录
create_cert_dir() {
    if [ ! -d "$CERT_DIR" ]; then
        log_info "创建证书目录: $CERT_DIR"
        if ! mkdir -p "$CERT_DIR"; then
            log_error "无法创建证书目录: $CERT_DIR"
            exit 1
        fi
        # 设置适当的权限
        chmod 700 "$CERT_DIR"
        log_success "证书目录创建完成"
    fi
}

# 设置Cloudflare API
setup_cloudflare_api() {
    if [ -z "$CF_Token" ]; then
        log_info "请设置Cloudflare API Token..."
        echo -n "请输入Cloudflare API Token: "
        read -r CF_Token

        if [ -z "$CF_Token" ]; then
            log_error "Cloudflare API Token不能为空"
            exit 1
        fi

        # 导出环境变量供acme.sh使用
        export CF_Token="$CF_Token"
        log_success "Cloudflare API Token已设置"
    else
        export CF_Token="$CF_Token"
        log_info "使用已配置的Cloudflare API Token"
    fi
}

# 申请SSL证书
issue_certificate() {
    local domain=$1

    log_info "为域名 $domain 申请SSL证书..."

    # 检查是否为通配符域名
    if [[ $domain == \*.* ]]; then
        log_info "检测到通配符域名，使用Cloudflare DNS验证模式..."

        # 设置Cloudflare API
        setup_cloudflare_api

        # 通配符域名必须使用DNS验证
        if ~/.acme.sh/acme.sh --issue -d "$domain" --dns dns_cf --force; then
            log_success "通配符证书申请成功: $domain"
        else
            log_error "通配符证书申请失败: $domain"
            log_info "请检查Cloudflare API Token是否正确"
            restart_nginx
            exit 1
        fi
    else
        # 普通域名使用standalone模式
        if ~/.acme.sh/acme.sh --issue -d "$domain" --standalone --force; then
            log_success "证书申请成功: $domain"
        else
            log_error "证书申请失败: $domain"
            restart_nginx  # 确保nginx重新启动
            exit 1
        fi
    fi
}

# 安装SSL证书
install_certificate() {
    local domain=$1

    log_info "安装SSL证书: $domain"

    if ~/.acme.sh/acme.sh --installcert -d "$domain" \
        --key-file "$CERT_DIR/${domain}-key.pem" \
        --fullchain-file "$CERT_DIR/${domain}-cert.pem" \
        --reloadcmd "$NGINX_CMD_RESTART"; then
        log_success "证书安装成功: $domain"
        # 设置证书文件权限
        chmod 600 "$CERT_DIR/${domain}-key.pem" 2>/dev/null || true
        chmod 644 "$CERT_DIR/${domain}-cert.pem" 2>/dev/null || true
    else
        log_error "证书安装失败: $domain"
        exit 1
    fi
}

# 创建nginx配置文件
create_nginx_config() {
    local domain=$1
    local config_file="$NGINX_CONF_DIR/$domain.conf"

    log_info "创建nginx配置文件: $domain"

    # 检查nginx配置目录是否存在
    if [ ! -d "$NGINX_CONF_DIR" ]; then
        log_warning "nginx配置目录不存在: $NGINX_CONF_DIR"
        log_info "尝试创建配置目录..."
        mkdir -p "$NGINX_CONF_DIR" || {
            log_error "无法创建nginx配置目录"
            return 1
        }
    fi

    # 询问用户是否需要创建nginx配置
    echo ""
    echo -n "是否需要自动创建nginx配置文件？(Y/n): "
    read -r create_config
    create_config=${create_config:-Y}

    case "$create_config" in
        [Yy]|[Yy][Ee][Ss])
            log_info "开始创建nginx配置文件..."

            # 询问监听端口
            echo -n "请输入HTTP端口 (默认80): "
            read -r http_port
            http_port=${http_port:-80}

            echo -n "请输入HTTPS端口 (默认443): "
            read -r https_port
            https_port=${https_port:-443}

            # 询问配置类型
            echo ""
            echo "请选择nginx配置类型："
            echo "1) 反向代理 (代理到后端端口)"
            echo "2) 静态网站 (需要网站根目录)"
            echo -n "请选择 (1/2): "
            read -r config_type

            case "$config_type" in
                1)
                    # 反向代理配置
                    echo -n "请输入后端服务端口 (例如: 3000): "
                    read -r backend_port

                    if [ -z "$backend_port" ]; then
                        log_error "后端端口不能为空"
                        return 1
                    fi

                    echo -n "请输入后端服务地址 (默认127.0.0.1): "
                    read -r backend_host
                    backend_host=${backend_host:-127.0.0.1}

                    # 询问是否需要WebSocket支持
                    echo -n "是否需要WebSocket支持？(y/n): "
                    read -r websocket_support

                    # 生成反向代理配置文件
                    cat > "$config_file" << EOF
# HTTP重定向到HTTPS
server {
    listen $http_port;
    server_name $domain;
    return 301 https://\$server_name\$request_uri;
}

# HTTPS反向代理配置
server {
    listen $https_port ssl http2;
    server_name $domain;

    # SSL证书配置
    ssl_certificate $CERT_DIR/${domain}-cert.pem;
    ssl_certificate_key $CERT_DIR/${domain}-key.pem;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;

    # 反向代理配置
    location / {
        proxy_pass http://$backend_host:$backend_port;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
EOF

                    # 如果需要WebSocket支持，添加相关配置
                    if [[ "$websocket_support" =~ ^[Yy] ]]; then
                        cat >> "$config_file" << EOF

        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
EOF
                    fi

                    cat >> "$config_file" << EOF
    }

    # 健康检查端点（可选）
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF
                    ;;
                2)
                    # 静态网站配置
                    echo -n "请输入网站根目录 (默认/var/www/$domain): "
                    read -r web_root
                    web_root=${web_root:-/var/www/$domain}

                    # 创建网站根目录
                    if [ ! -d "$web_root" ]; then
                        log_info "创建网站根目录: $web_root"
                        mkdir -p "$web_root"
                        echo "<h1>Welcome to $domain</h1>" > "$web_root/index.html"
                        chown -R www-data:www-data "$web_root" 2>/dev/null || true
                    fi

                    # 生成静态网站配置文件
                    cat > "$config_file" << EOF
# HTTP重定向到HTTPS
server {
    listen $http_port;
    server_name $domain;
    return 301 https://\$server_name\$request_uri;
}

# HTTPS配置
server {
    listen $https_port ssl http2;
    server_name $domain;

    root $web_root;
    index index.html index.htm index.php;

    # SSL证书配置
    ssl_certificate $CERT_DIR/${domain}-cert.pem;
    ssl_certificate_key $CERT_DIR/${domain}-key.pem;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;

    # 基本location配置
    location / {
        try_files \$uri \$uri/ =404;
    }

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF
                    ;;
                *)
                    log_error "无效选择，请选择1或2"
                    return 1
                    ;;
            esac

            log_success "nginx配置文件已创建: $config_file"

            # 启用配置文件（如果使用sites-enabled结构）
            if [ -d "$NGINX_ENABLED_DIR" ]; then
                ln -sf "$config_file" "$NGINX_ENABLED_DIR/$domain.conf" 2>/dev/null && {
                    log_success "配置文件已启用"
                } || {
                    log_warning "无法创建软链接，请手动启用配置"
                }
            fi

            # 测试nginx配置
            if nginx -t 2>/dev/null; then
                log_success "nginx配置测试通过"
                return 0
            else
                log_error "nginx配置测试失败，请检查配置文件"
                log_info "配置文件位置: $config_file"
                return 1
            fi
            ;;
        *)
            log_info "跳过nginx配置文件创建"
            log_info "请手动创建nginx配置文件，证书路径："
            echo "  ssl_certificate $CERT_DIR/${domain}-cert.pem;"
            echo "  ssl_certificate_key $CERT_DIR/${domain}-key.pem;"
            return 0
            ;;
    esac
}

# 设置自动续签
setup_auto_renewal() {
    log_info "设置自动续签..."

    # 检查是否已经安装了cron任务
    if crontab -l 2>/dev/null | grep -q "acme.sh --cron"; then
        log_info "自动续签已设置"
    else
        ~/.acme.sh/acme.sh --install-cronjob
        log_success "自动续签设置完成"
    fi
}

# 显示证书信息
show_certificate_info() {
    local domain=$1
    
    log_info "证书文件位置:"
    echo "  私钥文件: $CERT_DIR/${domain}-key.pem"
    echo "  证书文件: $CERT_DIR/${domain}-cert.pem"
    
    if [ -f "$CERT_DIR/${domain}-cert.pem" ]; then
        log_info "证书有效期:"
        openssl x509 -in "$CERT_DIR/${domain}-cert.pem" -noout -dates
    fi
}

# 列出所有证书
list_certificates() {
    log_info "已申请的SSL证书列表:"
    ~/.acme.sh/acme.sh --list
}

# 测试证书续签
test_renewal() {
    local domain=$1

    log_info "测试证书续签: $domain"
    if ~/.acme.sh/acme.sh --renew -d "$domain" --dry-run; then
        log_success "续签测试成功: $domain"
    else
        log_error "续签测试失败: $domain"
    fi
}

# 交互式测试续签
interactive_test_renewal() {
    echo ""
    echo "=== 测试证书续签 ==="
    echo ""

    # 先显示已有证书列表
    log_info "当前已申请的证书："
    ~/.acme.sh/acme.sh --list 2>/dev/null || {
        log_warning "没有找到已申请的证书"
        echo ""
        echo -n "按回车键返回主菜单..."
        read -r
        return
    }

    echo ""
    while true; do
        echo -n "请输入要测试续签的域名: "
        read -r domain

        if [ -z "$domain" ]; then
            log_warning "域名不能为空，请重新输入"
            continue
        fi

        if validate_domain_format "$domain"; then
            test_renewal "$domain"
            break
        else
            log_error "域名格式不正确，请重新输入"
            continue
        fi
    done

    echo ""
    echo -n "按回车键返回主菜单..."
    read -r
}

# 交互式输入域名
input_domain() {
    log_debug "进入域名输入函数"
    echo ""
    echo "=== SSL证书申请 ==="
    echo ""

    log_debug "开始域名输入循环"
    while true; do
        log_debug "提示用户输入域名"
        echo -n "请输入要申请SSL证书的域名: "

        log_debug "等待用户输入域名"
        read -r domain
        log_debug "用户输入域名: [$domain]"

        if [ -z "$domain" ]; then
            log_warning "域名不能为空，请重新输入"
            continue
        fi

        # 验证域名格式
        log_debug "开始验证域名格式: $domain"
        if validate_domain_format "$domain"; then
            log_debug "域名格式验证通过"
            echo ""
            log_info "您输入的域名是: $domain"
            echo -n "确认申请此域名的SSL证书吗？(y/n): "
            read -r confirm
            log_debug "用户确认输入: $confirm"

            case "$confirm" in
                [Yy]|[Yy][Ee][Ss])
                    log_debug "用户确认申请证书"
                    echo "$domain"
                    return 0
                    ;;
                [Nn]|[Nn][Oo])
                    echo ""
                    log_info "已取消，请重新输入域名"
                    continue
                    ;;
                *)
                    log_warning "请输入 y 或 n"
                    continue
                    ;;
            esac
        else
            log_error "域名格式不正确，请重新输入"
            log_info "域名格式示例: example.com 或 sub.example.com"
            continue
        fi
    done
}

# 交互式主菜单
show_interactive_menu() {
    echo ""
    echo "=== SSL证书管理工具 ==="
    echo ""
    echo "请选择操作："
    echo "1) 申请新的SSL证书"
    echo "2) 查看已申请的证书列表"
    echo "3) 测试证书续签"
    echo "4) 查看帮助信息"
    echo "5) 退出"
    echo ""
    echo -n "请输入选项 (1-5): "
}

# 显示帮助信息
show_help() {
    echo ""
    echo "=== SSL证书管理脚本帮助 ==="
    echo ""
    echo "功能说明："
    echo "  - 自动申请Let's Encrypt免费SSL证书"
    echo "  - 自动安装和配置证书到nginx"
    echo "  - 设置自动续签任务"
    echo "  - 管理多个域名证书"
    echo ""
    echo "使用方法："
    echo "  直接运行: $0                 - 进入交互式菜单"
    echo "  命令行:   $0 <域名>          - 直接为指定域名申请证书"
    echo "  查看列表: $0 --list          - 列出所有已申请的证书"
    echo "  测试续签: $0 --test <域名>   - 测试指定域名的证书续签"
    echo ""
    echo "注意事项："
    echo "  - 请确保域名已正确解析到当前服务器"
    echo "  - 脚本需要root权限运行"
    echo "  - 申请证书时会临时停止nginx服务"
    echo "  - 证书文件保存在: $CERT_DIR"
    echo ""
}

# 执行证书申请流程
execute_certificate_request() {
    local domain=$1

    log_info "开始为域名 $domain 申请SSL证书..."

    # 执行申请流程
    check_network
    init_acme
    create_cert_dir

    # 通配符域名不需要停止nginx
    if [[ ! $domain == \*.* ]]; then
        stop_nginx
    fi

    issue_certificate "$domain"
    install_certificate "$domain"

    # 创建nginx配置文件（在重启nginx之前）
    create_nginx_config "$domain"

    setup_auto_renewal

    # 重启nginx（此时配置文件已经存在）
    restart_nginx

    # 显示结果
    log_success "SSL证书申请完成!"
    show_certificate_info "$domain"

    log_info "提示: 证书将在到期前30天自动续签"
}

# 交互式主循环
interactive_mode() {
    log_debug "进入交互式模式"
    while true; do
        log_debug "显示交互式菜单"
        show_interactive_menu
        log_debug "等待用户输入选项"
        read -r choice
        log_debug "用户选择了选项: $choice"

        case "$choice" in
            1)
                log_debug "开始申请新的SSL证书流程"
                log_debug "准备调用input_domain函数"

                # 直接在这里处理域名输入，避免函数调用问题
                echo ""
                echo "=== SSL证书申请 ==="
                echo ""

                while true; do
                    echo -n "请输入要申请SSL证书的域名: "
                    read -r domain
                    log_debug "用户输入域名: [$domain]"

                    if [ -z "$domain" ]; then
                        log_warning "域名不能为空，请重新输入"
                        continue
                    fi

                    # 简单的域名格式检查
                    if [[ "$domain" =~ ^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
                        echo ""
                        log_info "您输入的域名是: $domain"
                        echo -n "确认申请此域名的SSL证书吗？(y/n): "
                        read -r confirm

                        case "$confirm" in
                            [Yy]|[Yy][Ee][Ss])
                                log_debug "用户确认申请证书"
                                execute_certificate_request "$domain"
                                echo ""
                                echo -n "按回车键返回主菜单..."
                                read -r
                                break
                                ;;
                            [Nn]|[Nn][Oo])
                                echo ""
                                log_info "已取消，请重新输入域名"
                                continue
                                ;;
                            *)
                                log_warning "请输入 y 或 n"
                                continue
                                ;;
                        esac
                    else
                        log_error "域名格式不正确，请重新输入"
                        log_info "域名格式示例: example.com 或 sub.example.com"
                        continue
                    fi
                done
                ;;
            2)
                echo ""
                echo "=== 已申请的证书列表 ==="
                echo ""
                list_certificates
                echo ""
                echo -n "按回车键返回主菜单..."
                read -r
                ;;
            3)
                interactive_test_renewal
                ;;
            4)
                show_help
                echo -n "按回车键返回主菜单..."
                read -r
                ;;
            5)
                echo ""
                log_info "感谢使用SSL证书管理工具，再见！"
                exit 0
                ;;
            *)
                echo ""
                log_warning "无效选项，请输入 1-5"
                echo ""
                ;;
        esac
    done
}

# 初始化日志
init_log() {
    # 创建日志文件
    touch "$LOG_FILE" 2>/dev/null || LOG_FILE="/tmp/ssl-cert-manager-$$.log"
    log_debug "日志文件: $LOG_FILE"
    log_debug "脚本开始执行，PID: $$"
}

# 主函数
main() {
    init_log
    log_debug "开始执行主函数"
    check_root
    check_dependencies
    detect_nginx_service

    case "${1:-}" in
        --help|-h)
            show_help
            exit 0
            ;;
        --list|-l)
            list_certificates
            exit 0
            ;;
        --test|-t)
            if [ -z "$2" ]; then
                log_error "请指定要测试的域名"
                show_help
                exit 1
            fi
            validate_domain "$2"
            test_renewal "$2"
            exit 0
            ;;
        "")
            # 无参数时进入交互模式
            interactive_mode
            ;;
        *)
            # 申请证书的主流程（命令行模式）
            DOMAIN="$1"
            validate_domain "$DOMAIN"
            execute_certificate_request "$DOMAIN"
            ;;
    esac
}

# 脚本入口
main "$@"
